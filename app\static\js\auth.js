// Authentication module for DreamBig Real Estate Platform

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.idToken = null;
        this.auth = null;
        this.initialized = false;
        
        this.initializeFirebase();
        this.setupEventListeners();
    }
    
    // Initialize Firebase
    initializeFirebase() {
        try {
            if (typeof firebase === 'undefined') {
                console.error('Firebase SDK not loaded');
                return;
            }
            
            firebase.initializeApp(FIREBASE_CONFIG);
            this.auth = firebase.auth();
            
            // Set up auth state observer
            this.auth.onAuthStateChanged((user) => {
                this.handleAuthStateChange(user);
            });
            
            this.initialized = true;
            console.log('Firebase initialized successfully');
        } catch (error) {
            console.error('Firebase initialization error:', error);
            showToast('Firebase initialization failed', 'error');
        }
    }
    
    // Handle authentication state changes
    async handleAuthStateChange(user) {
        if (user) {
            try {
                // Get ID token
                this.idToken = await user.getIdToken();
                
                // Login to backend
                const response = await this.loginToBackend(this.idToken);
                
                if (response.success) {
                    this.currentUser = response.data.user;
                    this.updateUI(true);
                    showToast(`Welcome back, ${this.currentUser.name}!`, 'success');
                } else {
                    throw new Error(response.message || 'Backend login failed');
                }
            } catch (error) {
                console.error('Auth state change error:', error);
                showToast('Authentication error: ' + error.message, 'error');
                this.logout();
            }
        } else {
            this.currentUser = null;
            this.idToken = null;
            this.updateUI(false);
        }
    }
    
    // Setup event listeners
    setupEventListeners() {
        // Login form
        document.getElementById('login-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
        
        // Register form
        document.getElementById('register-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // Forgot password form
        document.getElementById('forgot-password-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleForgotPassword();
        });
    }
    
    // Handle login
    async handleLogin() {
        const email = document.getElementById('login-email').value.trim();
        const password = document.getElementById('login-password').value;
        
        if (!this.validateLoginForm(email, password)) {
            return;
        }
        
        showLoading(true);
        
        try {
            const userCredential = await this.auth.signInWithEmailAndPassword(email, password);
            console.log('User signed in:', userCredential.user);
            closeModal('login-modal');
        } catch (error) {
            console.error('Login error:', error);
            showToast(this.getFirebaseErrorMessage(error), 'error');
        } finally {
            showLoading(false);
        }
    }
    
    // Handle registration
    async handleRegister() {
        const name = document.getElementById('register-name').value.trim();
        const email = document.getElementById('register-email').value.trim();
        const phone = document.getElementById('register-phone').value.trim();
        const password = document.getElementById('register-password').value;
        const role = document.getElementById('register-role').value;
        
        if (!this.validateRegisterForm(name, email, phone, password, role)) {
            return;
        }
        
        showLoading(true);
        
        try {
            // Create Firebase user
            const userCredential = await this.auth.createUserWithEmailAndPassword(email, password);
            
            // Update profile
            await userCredential.user.updateProfile({
                displayName: name
            });
            
            // Register with backend
            const registrationData = {
                name,
                email,
                phone,
                role,
                firebase_uid: userCredential.user.uid
            };
            
            const response = await this.registerWithBackend(registrationData);
            
            if (response.success) {
                showToast('Registration successful! Please verify your email.', 'success');
                closeModal('register-modal');
                
                // Send email verification
                await userCredential.user.sendEmailVerification();
            } else {
                throw new Error(response.message || 'Backend registration failed');
            }
            
        } catch (error) {
            console.error('Registration error:', error);

            // Handle specific case where user already exists
            if (error.code === 'auth/email-already-in-use') {
                showToast('This email is already registered. Please login instead.', 'warning');
                closeModal('register-modal');
                showLoginModal();
                // Pre-fill the email in login form
                document.getElementById('login-email').value = email;
            } else {
                showToast(this.getFirebaseErrorMessage(error), 'error');
            }
        } finally {
            showLoading(false);
        }
    }

    // Handle forgot password
    async handleForgotPassword() {
        const email = document.getElementById('forgot-email').value.trim();

        if (!email) {
            showToast('Please enter your email address', 'warning');
            return;
        }

        if (!UTILS.validateEmail(email)) {
            showToast('Please enter a valid email address', 'warning');
            return;
        }

        showLoading(true);

        try {
            await this.auth.sendPasswordResetEmail(email);
            showToast('Password reset email sent! Check your inbox.', 'success');
            closeModal('forgot-password-modal');
        } catch (error) {
            console.error('Password reset error:', error);
            showToast(this.getFirebaseErrorMessage(error), 'error');
        } finally {
            showLoading(false);
        }
    }
    
    // Login to backend
    async loginToBackend(idToken) {
        try {
            const response = await fetch(UTILS.getApiUrl(API_CONFIG.ENDPOINTS.LOGIN), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${idToken}`
                },
                body: JSON.stringify({ id_token: idToken })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                return { success: true, data };
            } else {
                return { success: false, message: data.detail || 'Login failed' };
            }
        } catch (error) {
            console.error('Backend login error:', error);
            return { success: false, message: 'Network error' };
        }
    }
    
    // Register with backend
    async registerWithBackend(userData) {
        try {
            const response = await fetch(UTILS.getApiUrl(API_CONFIG.ENDPOINTS.REGISTER), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
            
            const data = await response.json();
            
            if (response.ok) {
                return { success: true, data };
            } else {
                return { success: false, message: data.detail || 'Registration failed' };
            }
        } catch (error) {
            console.error('Backend registration error:', error);
            return { success: false, message: 'Network error' };
        }
    }
    
    // Logout
    async logout() {
        try {
            if (this.auth) {
                await this.auth.signOut();
            }
            showToast('Logged out successfully', 'info');
        } catch (error) {
            console.error('Logout error:', error);
            showToast('Logout error: ' + error.message, 'error');
        }
    }
    
    // Update UI based on auth state
    updateUI(isAuthenticated) {
        const authButtons = document.getElementById('auth-buttons');
        const userMenu = document.getElementById('user-menu');
        const userName = document.getElementById('user-name');
        
        if (isAuthenticated && this.currentUser) {
            authButtons.style.display = 'none';
            userMenu.style.display = 'block';
            userName.textContent = this.currentUser.name || 'User';
        } else {
            authButtons.style.display = 'flex';
            userMenu.style.display = 'none';
        }
    }
    
    // Validate login form
    validateLoginForm(email, password) {
        if (!email || !password) {
            showToast('Please fill in all fields', 'warning');
            return false;
        }
        
        if (!UTILS.validateEmail(email)) {
            showToast('Please enter a valid email address', 'warning');
            return false;
        }
        
        return true;
    }
    
    // Validate register form
    validateRegisterForm(name, email, phone, password, role) {
        if (!name || !email || !phone || !password || !role) {
            showToast('Please fill in all fields', 'warning');
            return false;
        }
        
        if (name.length < 2) {
            showToast('Name must be at least 2 characters long', 'warning');
            return false;
        }
        
        if (!UTILS.validateEmail(email)) {
            showToast('Please enter a valid email address', 'warning');
            return false;
        }
        
        if (!UTILS.validatePhone(phone)) {
            showToast('Please enter a valid phone number', 'warning');
            return false;
        }
        
        if (password.length < 6) {
            showToast('Password must be at least 6 characters long', 'warning');
            return false;
        }
        
        return true;
    }
    
    // Get user-friendly Firebase error messages
    getFirebaseErrorMessage(error) {
        const errorMessages = {
            'auth/user-not-found': 'No account found with this email address',
            'auth/wrong-password': 'Incorrect password',
            'auth/email-already-in-use': 'An account with this email already exists',
            'auth/weak-password': 'Password is too weak',
            'auth/invalid-email': 'Invalid email address',
            'auth/user-disabled': 'This account has been disabled',
            'auth/too-many-requests': 'Too many failed attempts. Please try again later',
            'auth/network-request-failed': 'Network error. Please check your connection'
        };
        
        return errorMessages[error.code] || error.message || 'An error occurred';
    }
    
    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }
    
    // Get ID token
    getIdToken() {
        return this.idToken;
    }
    
    // Check if user is authenticated
    isAuthenticated() {
        return !!this.currentUser && !!this.idToken;
    }
    
    // Refresh token
    async refreshToken() {
        if (this.auth.currentUser) {
            try {
                this.idToken = await this.auth.currentUser.getIdToken(true);
                return this.idToken;
            } catch (error) {
                console.error('Token refresh error:', error);
                throw error;
            }
        }
        return null;
    }
}

// Initialize auth manager
const authManager = new AuthManager();

// Global auth functions
window.showLoginModal = () => {
    document.getElementById('login-modal').classList.add('show');
};

window.showRegisterModal = () => {
    document.getElementById('register-modal').classList.add('show');
};

window.switchToLogin = () => {
    closeModal('register-modal');
    showLoginModal();
};

window.switchToRegister = () => {
    closeModal('login-modal');
    showRegisterModal();
};

window.showForgotPassword = () => {
    closeModal('login-modal');
    document.getElementById('forgot-password-modal').classList.add('show');
};

window.switchToLogin = () => {
    closeModal('register-modal');
    closeModal('forgot-password-modal');
    showLoginModal();
};

window.logout = () => {
    authManager.logout();
};

window.toggleUserDropdown = () => {
    const dropdown = document.getElementById('user-dropdown');
    dropdown.classList.toggle('show');
};

// Close dropdown when clicking outside
document.addEventListener('click', (e) => {
    const userMenu = document.getElementById('user-menu');
    const dropdown = document.getElementById('user-dropdown');
    
    if (userMenu && !userMenu.contains(e.target)) {
        dropdown?.classList.remove('show');
    }
});

// Export auth manager
window.authManager = authManager;
