<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DreamBig Real Estate Platform</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><i class="fas fa-home"></i> DreamBig</h2>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#properties" class="nav-link">Properties</a>
                <a href="#investments" class="nav-link">Investments</a>
                <a href="#services" class="nav-link">Services</a>
                <a href="#about" class="nav-link">About</a>
            </div>
            
            <div class="nav-auth">
                <div id="auth-buttons" class="auth-buttons">
                    <button class="btn btn-outline" onclick="showLoginModal()">Login</button>
                    <button class="btn btn-primary" onclick="showRegisterModal()">Sign Up</button>
                </div>
                
                <div id="user-menu" class="user-menu" style="display: none;">
                    <div class="user-avatar" onclick="toggleUserDropdown()">
                        <i class="fas fa-user"></i>
                        <span id="user-name">User</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <a href="#" onclick="showDashboard()"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="#" onclick="showProfile()"><i class="fas fa-user-edit"></i> Profile</a>
                        <a href="#" onclick="showFavorites()"><i class="fas fa-heart"></i> Favorites</a>
                        <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
            
            <div class="nav-toggle" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Home Section -->
        <section id="home" class="hero-section">
            <div class="hero-content">
                <h1>Find Your Dream Property</h1>
                <p>Discover the perfect home, investment opportunity, or commercial space with our AI-powered platform</p>
                
                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="search-input" placeholder="Search properties, locations, or keywords...">
                        <button class="search-btn" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="search-filters">
                        <select id="property-type">
                            <option value="">Property Type</option>
                            <option value="apartment">Apartment</option>
                            <option value="house">House</option>
                            <option value="villa">Villa</option>
                            <option value="commercial">Commercial</option>
                        </select>
                        
                        <select id="bhk-filter">
                            <option value="">BHK</option>
                            <option value="1">1 BHK</option>
                            <option value="2">2 BHK</option>
                            <option value="3">3 BHK</option>
                            <option value="4">4+ BHK</option>
                        </select>
                        
                        <input type="number" id="price-min" placeholder="Min Price">
                        <input type="number" id="price-max" placeholder="Max Price">
                        
                        <button class="btn btn-secondary" onclick="showAdvancedFilters()">
                            <i class="fas fa-filter"></i> More Filters
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Properties Section -->
        <section id="properties" class="properties-section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h2>Properties</h2>
                    <div class="view-controls">
                        <button class="view-btn active" data-view="grid"><i class="fas fa-th"></i></button>
                        <button class="view-btn" data-view="list"><i class="fas fa-list"></i></button>
                    </div>
                </div>
                
                <div class="properties-grid" id="properties-grid">
                    <!-- Properties will be loaded here -->
                </div>
                
                <div class="load-more">
                    <button class="btn btn-outline" onclick="loadMoreProperties()">Load More Properties</button>
                </div>
            </div>
        </section>

        <!-- Dashboard Section -->
        <section id="dashboard" class="dashboard-section" style="display: none;">
            <div class="container">
                <h2>Dashboard</h2>
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3><i class="fas fa-heart"></i> Favorites</h3>
                        <div id="favorites-list"></div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3><i class="fas fa-eye"></i> Recently Viewed</h3>
                        <div id="recently-viewed-list"></div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3><i class="fas fa-chart-line"></i> Analytics</h3>
                        <div id="user-analytics"></div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('login-modal')">&times;</span>
            <h2>Login</h2>
            <form id="login-form">
                <div class="form-group">
                    <input type="email" id="login-email" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <input type="password" id="login-password" placeholder="Password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Login</button>
            </form>
            <p class="modal-footer">
                Don't have an account? <a href="#" onclick="switchToRegister()">Sign up</a>
            </p>
        </div>
    </div>

    <div id="register-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('register-modal')">&times;</span>
            <h2>Sign Up</h2>
            <form id="register-form">
                <div class="form-group">
                    <input type="text" id="register-name" placeholder="Full Name" required>
                </div>
                <div class="form-group">
                    <input type="email" id="register-email" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <input type="tel" id="register-phone" placeholder="Phone Number" required>
                </div>
                <div class="form-group">
                    <input type="password" id="register-password" placeholder="Password" required>
                </div>
                <div class="form-group">
                    <select id="register-role" required>
                        <option value="">Select Role</option>
                        <option value="tenant">Tenant</option>
                        <option value="owner">Property Owner</option>
                        <option value="investor">Investor</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Sign Up</button>
            </form>
            <p class="modal-footer">
                Already have an account? <a href="#" onclick="switchToLogin()">Login</a>
            </p>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner" style="display: none;">
        <div class="spinner"></div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- JavaScript Files -->
    <script src="js/config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>
    <script src="js/properties.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
