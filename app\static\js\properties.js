// Properties module for DreamBig Real Estate Platform

class PropertiesManager {
    constructor() {
        this.properties = [];
        this.currentPage = 0;
        this.hasMore = true;
        this.currentFilters = {};
        this.currentView = 'grid';
        this.favorites = new Set();
        
        this.setupEventListeners();
        this.loadInitialProperties();
    }
    
    // Setup event listeners
    setupEventListeners() {
        // View toggle buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.toggleView(e.target.dataset.view);
            });
        });
        
        // Search functionality
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            const debouncedSearch = UTILS.debounce(() => {
                this.performSearch();
            }, APP_CONFIG.DEBOUNCE_DELAY);
            
            searchInput.addEventListener('input', debouncedSearch);
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }
        
        // Filter changes
        ['property-type', 'bhk-filter', 'price-min', 'price-max'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateFilters();
                });
            }
        });
    }
    
    // Load initial properties
    async loadInitialProperties() {
        await this.loadProperties(true);
        await this.loadUserFavorites();
    }
    
    // Load properties
    async loadProperties(reset = false) {
        if (reset) {
            this.properties = [];
            this.currentPage = 0;
            this.hasMore = true;
        }
        
        if (!this.hasMore) return;
        
        const params = {
            skip: this.currentPage * APP_CONFIG.PAGINATION.DEFAULT_LIMIT,
            limit: APP_CONFIG.PAGINATION.DEFAULT_LIMIT,
            ...this.currentFilters
        };
        
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getProperties(params),
            { showLoading: this.properties.length === 0 }
        );
        
        if (response) {
            if (reset) {
                this.properties = response;
            } else {
                this.properties.push(...response);
            }
            
            this.hasMore = response.length === APP_CONFIG.PAGINATION.DEFAULT_LIMIT;
            this.currentPage++;
            
            this.renderProperties();
        }
    }
    
    // Load user favorites
    async loadUserFavorites() {
        if (!authManager.isAuthenticated()) return;
        
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getFavorites(),
            { showLoading: false }
        );
        
        if (response) {
            this.favorites = new Set(response.map(fav => fav.property_id));
            this.updateFavoriteButtons();
        }
    }
    
    // Perform search
    async performSearch() {
        const query = document.getElementById('search-input').value.trim();
        
        if (query.length > 0) {
            this.currentFilters.query = query;
        } else {
            delete this.currentFilters.query;
        }
        
        await this.loadProperties(true);
    }
    
    // Update filters
    async updateFilters() {
        const propertyType = document.getElementById('property-type').value;
        const bhk = document.getElementById('bhk-filter').value;
        const priceMin = document.getElementById('price-min').value;
        const priceMax = document.getElementById('price-max').value;
        
        this.currentFilters = {};
        
        if (propertyType) this.currentFilters.property_type = propertyType;
        if (bhk) this.currentFilters.bhk = parseInt(bhk);
        if (priceMin) this.currentFilters.price_min = parseFloat(priceMin);
        if (priceMax) this.currentFilters.price_max = parseFloat(priceMax);
        
        await this.loadProperties(true);
    }
    
    // Toggle view (grid/list)
    toggleView(view) {
        this.currentView = view;
        
        // Update button states
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });
        
        // Update grid class
        const grid = document.getElementById('properties-grid');
        if (grid) {
            grid.classList.toggle('list-view', view === 'list');
        }
    }
    
    // Render properties
    renderProperties() {
        const grid = document.getElementById('properties-grid');
        if (!grid) return;
        
        if (this.properties.length === 0) {
            grid.innerHTML = `
                <div class="no-properties">
                    <i class="fas fa-home" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
                    <h3>No properties found</h3>
                    <p>Try adjusting your search criteria or filters</p>
                </div>
            `;
            return;
        }
        
        grid.innerHTML = this.properties.map(property => this.createPropertyCard(property)).join('');
        this.updateFavoriteButtons();
    }
    
    // Create property card HTML
    createPropertyCard(property) {
        const isFavorite = this.favorites.has(property.id);
        const propertyTypeIcon = UTILS.getPropertyTypeIcon(property.property_type);
        
        return `
            <div class="property-card" data-property-id="${property.id}">
                <div class="property-image">
                    <img src="${property.images?.[0]?.url || '/static/images/property-placeholder.jpg'}" 
                         alt="${property.title}" 
                         onerror="this.src='/static/images/property-placeholder.jpg'">
                    <div class="property-badge">${UTILS.capitalize(property.property_type)}</div>
                </div>
                
                <div class="property-content">
                    <h3 class="property-title">${property.title}</h3>
                    
                    <div class="property-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${property.location}</span>
                    </div>
                    
                    <div class="property-details">
                        <div class="property-detail">
                            <i class="fas fa-bed"></i>
                            <span>${property.bhk} BHK</span>
                        </div>
                        <div class="property-detail">
                            <i class="fas fa-ruler-combined"></i>
                            <span>${property.area} sq ft</span>
                        </div>
                        <div class="property-detail">
                            <i class="${propertyTypeIcon}"></i>
                            <span>${UTILS.capitalize(property.property_type)}</span>
                        </div>
                    </div>
                    
                    <div class="property-price">
                        ${UTILS.formatCurrency(property.price)}
                    </div>
                    
                    <div class="property-actions">
                        <button class="btn btn-primary" onclick="showPropertyDetails(${property.id})">
                            View Details
                        </button>
                        
                        <button class="btn-icon favorite-btn ${isFavorite ? 'active' : ''}" 
                                onclick="toggleFavorite(${property.id})"
                                title="${isFavorite ? 'Remove from favorites' : 'Add to favorites'}">
                            <i class="fas fa-heart"></i>
                        </button>
                        
                        <button class="btn-icon" onclick="shareProperty(${property.id})" title="Share">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Toggle favorite
    async toggleFavorite(propertyId) {
        if (!authManager.isAuthenticated()) {
            showToast('Please login to add favorites', 'warning');
            showLoginModal();
            return;
        }
        
        const isFavorite = this.favorites.has(propertyId);
        
        const response = await ApiUtils.handleApiCall(
            () => isFavorite 
                ? apiClient.removeFromFavorites(propertyId)
                : apiClient.addToFavorites(propertyId),
            { 
                showLoading: false,
                successMessage: isFavorite ? 'Removed from favorites' : 'Added to favorites'
            }
        );
        
        if (response) {
            if (isFavorite) {
                this.favorites.delete(propertyId);
            } else {
                this.favorites.add(propertyId);
            }
            
            this.updateFavoriteButtons();
        }
    }
    
    // Update favorite button states
    updateFavoriteButtons() {
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            const propertyId = parseInt(btn.closest('.property-card').dataset.propertyId);
            const isFavorite = this.favorites.has(propertyId);
            
            btn.classList.toggle('active', isFavorite);
            btn.title = isFavorite ? 'Remove from favorites' : 'Add to favorites';
        });
    }
    
    // Show property details
    async showPropertyDetails(propertyId) {
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getProperty(propertyId),
            { errorMessage: 'Failed to load property details' }
        );
        
        if (response) {
            this.renderPropertyModal(response.property);
        }
    }
    
    // Render property details modal
    renderPropertyModal(property) {
        const modalHtml = `
            <div id="property-detail-modal" class="modal property-detail-modal">
                <div class="modal-content">
                    <span class="close" onclick="closeModal('property-detail-modal')">&times;</span>
                    
                    <div class="property-gallery">
                        <img src="${property.images?.[0]?.url || '/static/images/property-placeholder.jpg'}" 
                             alt="${property.title}">
                    </div>
                    
                    <div class="property-info-grid">
                        <div class="property-main-info">
                            <h2>${property.title}</h2>
                            <div class="property-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${property.location}</span>
                            </div>
                            
                            <div class="property-price">
                                ${UTILS.formatCurrency(property.price)}
                            </div>
                            
                            <div class="property-features">
                                <div class="feature-item">
                                    <i class="fas fa-bed"></i>
                                    <span>${property.bhk} BHK</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-ruler-combined"></i>
                                    <span>${property.area} sq ft</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-couch"></i>
                                    <span>${UTILS.capitalize(property.furnishing || 'Not specified')}</span>
                                </div>
                            </div>
                            
                            <div class="property-description">
                                <h4>Description</h4>
                                <p>${property.description || 'No description available'}</p>
                            </div>
                            
                            ${property.amenities && property.amenities.length > 0 ? `
                                <div class="property-amenities">
                                    <h4>Amenities</h4>
                                    <div class="amenities-grid">
                                        ${property.amenities.map(amenity => `
                                            <div class="amenity-item">
                                                <i class="${UTILS.getAmenityIcon(amenity)}"></i>
                                                <span>${amenity}</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        
                        <div class="property-contact">
                            <h4>Contact Information</h4>
                            <div class="contact-info">
                                <span><i class="fas fa-user"></i> ${property.owner_name || 'Owner'}</span>
                                <span><i class="fas fa-phone"></i> ${property.owner_phone || 'Not available'}</span>
                                <span><i class="fas fa-envelope"></i> ${property.owner_email || 'Not available'}</span>
                            </div>
                            
                            <button class="btn btn-primary btn-full" onclick="contactOwner(${property.id})">
                                Contact Owner
                            </button>
                            
                            <button class="btn btn-outline btn-full" onclick="scheduleVisit(${property.id})">
                                Schedule Visit
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing modal
        const existingModal = document.getElementById('property-detail-modal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // Add new modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        document.getElementById('property-detail-modal').classList.add('show');
    }
    
    // Share property
    shareProperty(propertyId) {
        const property = this.properties.find(p => p.id === propertyId);
        if (!property) return;
        
        const shareData = {
            title: property.title,
            text: `Check out this property: ${property.title} in ${property.location}`,
            url: `${window.location.origin}/property/${propertyId}`
        };
        
        if (navigator.share) {
            navigator.share(shareData);
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareData.url).then(() => {
                showToast('Property link copied to clipboard', 'success');
            });
        }
    }
}

// Initialize properties manager
const propertiesManager = new PropertiesManager();

// Global functions
window.performSearch = () => {
    propertiesManager.performSearch();
};

window.loadMoreProperties = () => {
    propertiesManager.loadProperties();
};

window.toggleFavorite = (propertyId) => {
    propertiesManager.toggleFavorite(propertyId);
};

window.showPropertyDetails = (propertyId) => {
    propertiesManager.showPropertyDetails(propertyId);
};

window.shareProperty = (propertyId) => {
    propertiesManager.shareProperty(propertyId);
};

window.contactOwner = (propertyId) => {
    showToast('Contact feature coming soon!', 'info');
};

window.scheduleVisit = (propertyId) => {
    showToast('Visit scheduling feature coming soon!', 'info');
};

window.showAdvancedFilters = () => {
    showToast('Advanced filters coming soon!', 'info');
};

// Export properties manager
window.propertiesManager = propertiesManager;
