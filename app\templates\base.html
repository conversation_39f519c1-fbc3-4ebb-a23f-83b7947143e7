<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DreamBig Real Estate Platform{% endblock %}</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/components.css">
    <link rel="stylesheet" href="/static/css/responsive.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><i class="fas fa-home"></i> DreamBig</h2>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <a href="/" class="nav-link {% if request.url.path == '/' %}active{% endif %}">Home</a>
                <a href="/properties" class="nav-link {% if request.url.path == '/properties' %}active{% endif %}">Properties</a>
                <a href="/investments" class="nav-link {% if request.url.path == '/investments' %}active{% endif %}">Investments</a>
                <a href="/services" class="nav-link {% if request.url.path == '/services' %}active{% endif %}">Services</a>
                <a href="/about" class="nav-link {% if request.url.path == '/about' %}active{% endif %}">About</a>
            </div>
            
            <div class="nav-auth">
                <div id="auth-buttons" class="auth-buttons">
                    <button class="btn btn-outline" onclick="showLoginModal()">Login</button>
                    <button class="btn btn-primary" onclick="showRegisterModal()">Sign Up</button>
                </div>
                
                <div id="user-menu" class="user-menu" style="display: none;">
                    <div class="user-avatar" onclick="toggleUserDropdown()">
                        <i class="fas fa-user"></i>
                        <span id="user-name">User</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        <a href="#" onclick="showProfile()"><i class="fas fa-user-edit"></i> Profile</a>
                        <a href="#" onclick="showFavorites()"><i class="fas fa-heart"></i> Favorites</a>
                        <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
            
            <div class="nav-toggle" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Modals -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('login-modal')">&times;</span>
            <h2>Login</h2>
            <form id="login-form">
                <div class="form-group">
                    <input type="email" id="login-email" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <input type="password" id="login-password" placeholder="Password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Login</button>
            </form>
            <p class="modal-footer">
                <a href="#" onclick="showForgotPassword()">Forgot Password?</a><br>
                Don't have an account? <a href="#" onclick="switchToRegister()">Sign up</a>
            </p>
        </div>
    </div>

    <div id="register-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('register-modal')">&times;</span>
            <h2>Sign Up</h2>
            <form id="register-form">
                <div class="form-group">
                    <input type="text" id="register-name" placeholder="Full Name" required>
                </div>
                <div class="form-group">
                    <input type="email" id="register-email" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <input type="tel" id="register-phone" placeholder="Phone Number" required>
                </div>
                <div class="form-group">
                    <input type="password" id="register-password" placeholder="Password" required>
                </div>
                <div class="form-group">
                    <select id="register-role" required>
                        <option value="">Select Role</option>
                        <option value="tenant">Tenant</option>
                        <option value="owner">Property Owner</option>
                        <option value="investor">Investor</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Sign Up</button>
            </form>
            <p class="modal-footer">
                Already have an account? <a href="#" onclick="switchToLogin()">Login</a>
            </p>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div id="forgot-password-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('forgot-password-modal')">&times;</span>
            <h2>Reset Password</h2>
            <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">
                Enter your email address and we'll send you a link to reset your password.
            </p>
            <form id="forgot-password-form">
                <div class="form-group">
                    <input type="email" id="forgot-email" placeholder="Email" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Send Reset Link</button>
            </form>
            <p class="modal-footer">
                Remember your password? <a href="#" onclick="switchToLogin()">Back to Login</a>
            </p>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner" style="display: none;">
        <div class="spinner"></div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- JavaScript Files -->
    <script src="/static/js/config.js"></script>
    <script src="/static/js/auth.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/properties.js"></script>
    <script src="/static/js/dashboard.js"></script>
    <script src="/static/js/main.js"></script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
