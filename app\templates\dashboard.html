{% extends "base.html" %}

{% block title %}Dashboard - DreamBig Real Estate Platform{% endblock %}

{% block content %}
    <!-- Dashboard Section -->
    <section class="dashboard-section" style="margin-top: 80px; padding-top: 2rem;">
        <div class="container">
            <div class="section-header">
                <h2>Dashboard</h2>
                <div class="dashboard-actions">
                    <button class="btn btn-primary" onclick="showProfile()">
                        <i class="fas fa-user-edit"></i> Edit Profile
                    </button>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3><i class="fas fa-heart"></i> Favorites</h3>
                    <div id="favorites-list">
                        <!-- Favorites will be loaded here -->
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h3><i class="fas fa-eye"></i> Recently Viewed</h3>
                    <div id="recently-viewed-list">
                        <!-- Recently viewed will be loaded here -->
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h3><i class="fas fa-chart-line"></i> Analytics</h3>
                    <div id="user-analytics">
                        <!-- Analytics will be loaded here -->
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h3><i class="fas fa-lightbulb"></i> Recommendations</h3>
                    <div id="recommendations-list">
                        <!-- Recommendations will be loaded here -->
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions" style="margin-top: 2rem;">
                <h3>Quick Actions</h3>
                <div class="actions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <button class="btn btn-outline" onclick="window.location.href='/properties'">
                        <i class="fas fa-search"></i> Browse Properties
                    </button>
                    <button class="btn btn-outline" onclick="showCreatePropertyModal()">
                        <i class="fas fa-plus"></i> List Property
                    </button>
                    <button class="btn btn-outline" onclick="window.location.href='/investments'">
                        <i class="fas fa-chart-line"></i> View Investments
                    </button>
                    <button class="btn btn-outline" onclick="window.location.href='/services'">
                        <i class="fas fa-tools"></i> Book Services
                    </button>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Authentication Required Message -->
    <div id="auth-required" class="auth-required" style="display: none; text-align: center; padding: 4rem 2rem; margin-top: 80px;">
        <div class="container">
            <i class="fas fa-lock" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
            <h2>Authentication Required</h2>
            <p>Please login to access your dashboard</p>
            <div style="margin-top: 2rem;">
                <button class="btn btn-primary" onclick="showLoginModal()">Login</button>
                <button class="btn btn-outline" onclick="showRegisterModal()">Sign Up</button>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Initialize dashboard page
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is authenticated
        setTimeout(function() {
            if (authManager && authManager.isAuthenticated()) {
                document.querySelector('.dashboard-section').style.display = 'block';
                document.getElementById('auth-required').style.display = 'none';
                
                // Load dashboard data
                if (window.dashboardManager) {
                    dashboardManager.loadDashboardData();
                }
            } else {
                document.querySelector('.dashboard-section').style.display = 'none';
                document.getElementById('auth-required').style.display = 'block';
            }
        }, 500); // Wait for auth manager to initialize
    });
    
    // Listen for auth state changes
    document.addEventListener('authStateChanged', function(e) {
        if (e.detail.isAuthenticated) {
            document.querySelector('.dashboard-section').style.display = 'block';
            document.getElementById('auth-required').style.display = 'none';
            
            if (window.dashboardManager) {
                dashboardManager.loadDashboardData();
            }
        } else {
            document.querySelector('.dashboard-section').style.display = 'none';
            document.getElementById('auth-required').style.display = 'block';
        }
    });
    
    // Quick action functions
    function showCreatePropertyModal() {
        showToast('Property creation feature coming soon!', 'info');
    }
</script>
{% endblock %}
