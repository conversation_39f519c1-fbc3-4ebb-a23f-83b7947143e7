{% extends "base.html" %}

{% block title %}DreamBig Real Estate Platform - Find Your Dream Property{% endblock %}

{% block content %}
    <!-- Home Section -->
    <section id="home" class="hero-section">
            <div class="hero-content">
                <h1>Find Your Dream Property</h1>
                <p>Discover the perfect home, investment opportunity, or commercial space with our AI-powered platform</p>
                
                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="search-input" placeholder="Search properties, locations, or keywords...">
                        <button class="search-btn" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="search-filters">
                        <select id="property-type">
                            <option value="">Property Type</option>
                            <option value="apartment">Apartment</option>
                            <option value="house">House</option>
                            <option value="villa">Villa</option>
                            <option value="commercial">Commercial</option>
                        </select>
                        
                        <select id="bhk-filter">
                            <option value="">BHK</option>
                            <option value="1">1 BHK</option>
                            <option value="2">2 BHK</option>
                            <option value="3">3 BHK</option>
                            <option value="4">4+ BHK</option>
                        </select>
                        
                        <input type="number" id="price-min" placeholder="Min Price">
                        <input type="number" id="price-max" placeholder="Max Price">
                        
                        <button class="btn btn-secondary" onclick="showAdvancedFilters()">
                            <i class="fas fa-filter"></i> More Filters
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Properties Section -->
        <section id="properties" class="properties-section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h2>Properties</h2>
                    <div class="view-controls">
                        <button class="view-btn active" data-view="grid"><i class="fas fa-th"></i></button>
                        <button class="view-btn" data-view="list"><i class="fas fa-list"></i></button>
                    </div>
                </div>
                
                <div class="properties-grid" id="properties-grid">
                    <!-- Properties will be loaded here -->
                </div>
                
                <div class="load-more">
                    <button class="btn btn-outline" onclick="loadMoreProperties()">Load More Properties</button>
                </div>
            </div>
        </section>

        <!-- Dashboard Section -->
        <section id="dashboard" class="dashboard-section" style="display: none;">
            <div class="container">
                <h2>Dashboard</h2>
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3><i class="fas fa-heart"></i> Favorites</h3>
                        <div id="favorites-list"></div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3><i class="fas fa-eye"></i> Recently Viewed</h3>
                        <div id="recently-viewed-list"></div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3><i class="fas fa-chart-line"></i> Analytics</h3>
                        <div id="user-analytics"></div>
                    </div>
                </div>
            </div>
        </section>

{% endblock %}
