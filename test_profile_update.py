#!/usr/bin/env python3
"""
Test script to verify profile update functionality
"""

import requests
import json

def test_profile_update():
    """Test profile update with mock authentication"""
    
    print("🧪 Testing Profile Update")
    print("=" * 40)
    
    # Mock update data
    update_data = {
        "name": "Updated Test User",
        "phone": "9876543296"
    }
    
    print(f"📝 Update data: {update_data}")
    
    # This test requires a valid authentication token
    # In real scenario, you'd get this from successful login
    mock_token = "mock_firebase_token"
    
    try:
        response = requests.put(
            "http://127.0.0.1:8000/api/v1/users/me",
            json=update_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {mock_token}"
            }
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Expected 401 error for mock token - endpoint is working")
            print("💡 To test profile update properly:")
            print("   1. Login through the web interface")
            print("   2. Use browser developer tools to get the real auth token")
            print("   3. Test the update through the web interface")
            return True
        elif response.status_code == 200:
            data = response.json()
            print("✅ Profile update successful!")
            print(f"   Updated user: {data}")
            return True
        else:
            try:
                error_data = response.json()
                print(f"❌ Profile update failed: {error_data.get('detail')}")
            except:
                print(f"❌ Profile update failed: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure your FastAPI server is running")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_database_user_update():
    """Test user update directly in database"""
    
    print("\n🗄️ Testing Database User Update")
    
    try:
        from app.db.session import get_db
        from app.db import crud
        
        db = next(get_db())
        
        # Find a test user
        users = crud.get_users(db, skip=0, limit=5)
        if not users:
            print("❌ No users found in database")
            return False
        
        test_user = users[0]
        print(f"📝 Testing update for user: {test_user.email} (ID: {test_user.id})")
        
        # Test update data
        update_data = {
            "name": f"Updated {test_user.name}",
            "phone": "9999999999"  # Use a unique phone number
        }
        
        print(f"📝 Update data: {update_data}")
        
        # Perform update
        updated_user = crud.update_user(db, user_id=test_user.id, user_update=update_data)
        
        if updated_user:
            print("✅ Database update successful!")
            print(f"   New name: {updated_user.name}")
            print(f"   New phone: {updated_user.phone}")
            
            # Revert the changes
            revert_data = {
                "name": test_user.name,
                "phone": test_user.phone
            }
            crud.update_user(db, user_id=test_user.id, user_update=revert_data)
            print("🔄 Changes reverted")
            
            return True
        else:
            print("❌ Database update failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test error: {str(e)}")
        return False
    finally:
        try:
            db.close()
        except:
            pass

def test_validation():
    """Test validation logic"""
    
    print("\n✅ Testing Validation Logic")
    
    # Test cases
    test_cases = [
        {"name": "", "phone": "9876543210", "expected": "fail", "reason": "empty name"},
        {"name": "A", "phone": "9876543210", "expected": "fail", "reason": "name too short"},
        {"name": "Valid Name", "phone": "", "expected": "fail", "reason": "empty phone"},
        {"name": "Valid Name", "phone": "123", "expected": "fail", "reason": "phone too short"},
        {"name": "Valid Name", "phone": "9876543210", "expected": "pass", "reason": "valid data"},
        {"name": "Valid Name", "phone": "98765-43210", "expected": "pass", "reason": "phone with dashes"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['reason']}")
        print(f"   Input: name='{test_case['name']}', phone='{test_case['phone']}'")
        
        # Simulate frontend validation
        name = test_case['name'].strip()
        import re
        phone = re.sub(r'\D', '', test_case['phone']) if test_case['phone'] else ''
        
        # Validation logic (same as frontend)
        if not name or not phone:
            result = "fail"
            reason = "empty fields"
        elif len(name) < 2:
            result = "fail"
            reason = "name too short"
        elif len(phone) < 10:
            result = "fail"
            reason = "phone too short"
        else:
            result = "pass"
            reason = "valid"
        
        expected = test_case['expected']
        if result == expected:
            print(f"   ✅ PASS: {reason}")
        else:
            print(f"   ❌ FAIL: Expected {expected}, got {result} ({reason})")

if __name__ == "__main__":
    print("🚀 DreamBig Profile Update Test")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:8000/")
        if response.status_code != 200:
            print("❌ Server is not responding correctly")
            exit(1)
        print("✅ Server is running")
    except:
        print("❌ Server is not running!")
        print("   Please start the server with:")
        print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        exit(1)
    
    # Test validation logic
    test_validation()
    
    # Test database update
    db_ok = test_database_user_update()
    
    # Test API endpoint
    api_ok = test_profile_update()
    
    print("\n" + "=" * 50)
    print("🎯 Manual Testing Instructions:")
    print("1. 🚀 Start server: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("2. 🌐 Visit: http://127.0.0.1:8000/")
    print("3. 🔑 Login with your test account")
    print("4. 👤 Click user menu → Profile")
    print("5. ✏️ Update name and phone number")
    print("6. 💾 Click 'Update Profile'")
    print("7. ✅ Check for success message")
    print("\n🔍 If it fails:")
    print("   - Check browser console for errors")
    print("   - Check server logs for detailed error messages")
    print("   - Verify phone number is unique")
    
    if db_ok and api_ok:
        print("\n🎉 All automated tests passed!")
    else:
        print("\n❌ Some tests failed - check the errors above")
