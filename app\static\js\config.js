// Configuration file for the DreamBig Real Estate Platform

// API Configuration
const API_CONFIG = {
    BASE_URL: 'http://127.0.0.1:8000',
    API_VERSION: '/api/v1',
    ENDPOINTS: {
        // Authentication
        LOGIN: '/auth/login',
        REGISTER: '/auth/register',
        TEST_TOKEN: '/auth/test-token',
        
        // Users
        USER_ME: '/users/me',
        USER_UPDATE: '/users/me',
        USER_PREFERENCES: '/users/preferences',
        USER_FAVORITES: '/users/favorites',
        USER_RECENTLY_VIEWED: '/users/recently-viewed',
        USER_RECOMMENDATIONS: '/users/recommendations',
        USER_ANALYTICS: '/users/analytics',
        
        // Properties
        PROPERTIES: '/properties',
        PROPERTY_DETAIL: '/properties/{id}',
        PROPERTY_UPLOAD_IMAGE: '/properties/{id}/upload-image',
        
        // Search
        SEARCH: '/search',
        
        // Investments
        INVESTMENTS: '/investments',
        INVESTMENT_DETAIL: '/investments/{id}',
        
        // Services
        SERVICES: '/services',
        SERVICE_PROVIDERS: '/services/providers',
        SERVICE_BOOKINGS: '/services/bookings'
    }
};

// Firebase Configuration
const FIREBASE_CONFIG = {
    apiKey: "AIzaSyCzhyGXIVMPtMcpSPZPR-q8keBVkeBvySM",
    authDomain: "dreambig-6d10e.firebaseapp.com",
    projectId: "dreambig-6d10e",
    storageBucket: "dreambig-6d10e.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
};

// Application Configuration
const APP_CONFIG = {
    APP_NAME: 'DreamBig Real Estate Platform',
    VERSION: '1.0.0',
    PAGINATION: {
        DEFAULT_LIMIT: 20,
        MAX_LIMIT: 100
    },
    TOAST_DURATION: 5000,
    DEBOUNCE_DELAY: 300,
    IMAGE_UPLOAD: {
        MAX_SIZE: 5 * 1024 * 1024, // 5MB
        ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
        MAX_FILES: 10
    }
};

// Property Types and Options
const PROPERTY_OPTIONS = {
    TYPES: [
        { value: 'apartment', label: 'Apartment' },
        { value: 'house', label: 'House' },
        { value: 'villa', label: 'Villa' },
        { value: 'commercial', label: 'Commercial' },
        { value: 'plot', label: 'Plot/Land' }
    ],
    
    BHK_OPTIONS: [
        { value: '1', label: '1 BHK' },
        { value: '2', label: '2 BHK' },
        { value: '3', label: '3 BHK' },
        { value: '4', label: '4 BHK' },
        { value: '5', label: '5+ BHK' }
    ],
    
    FURNISHING: [
        { value: 'unfurnished', label: 'Unfurnished' },
        { value: 'semi-furnished', label: 'Semi-Furnished' },
        { value: 'fully-furnished', label: 'Fully Furnished' }
    ],
    
    PARKING: [
        { value: 'none', label: 'No Parking' },
        { value: 'bike', label: 'Bike Parking' },
        { value: 'car', label: 'Car Parking' },
        { value: 'both', label: 'Both' }
    ],
    
    AMENITIES: [
        'Swimming Pool', 'Gym', 'Garden', 'Security', 'Elevator',
        'Power Backup', 'Water Supply', 'Parking', 'Playground',
        'Club House', 'Shopping Center', 'Hospital Nearby',
        'School Nearby', 'Metro Station', 'Bus Stop'
    ]
};

// User Roles
const USER_ROLES = {
    TENANT: 'tenant',
    OWNER: 'owner',
    INVESTOR: 'investor',
    ADMIN: 'admin'
};

// Investment Risk Levels
const INVESTMENT_RISK_LEVELS = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high'
};

// Service Categories
const SERVICE_CATEGORIES = {
    LEGAL: 'legal',
    FINANCIAL: 'financial',
    MAINTENANCE: 'maintenance',
    INTERIOR: 'interior',
    MOVING: 'moving'
};

// Utility Functions
const UTILS = {
    // Format currency
    formatCurrency: (amount, currency = '₹') => {
        if (!amount) return `${currency}0`;
        
        // Convert to Indian numbering system (lakhs, crores)
        if (amount >= 10000000) { // 1 crore
            return `${currency}${(amount / 10000000).toFixed(2)} Cr`;
        } else if (amount >= 100000) { // 1 lakh
            return `${currency}${(amount / 100000).toFixed(2)} L`;
        } else if (amount >= 1000) { // 1 thousand
            return `${currency}${(amount / 1000).toFixed(0)}K`;
        }
        
        return `${currency}${amount.toLocaleString('en-IN')}`;
    },
    
    // Format date
    formatDate: (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },
    
    // Format relative time
    formatRelativeTime: (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;
        
        return UTILS.formatDate(dateString);
    },
    
    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Generate UUID
    generateUUID: () => {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },
    
    // Validate email
    validateEmail: (email) => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    // Validate phone
    validatePhone: (phone) => {
        const re = /^[6-9]\d{9}$/;
        return re.test(phone.replace(/\D/g, ''));
    },
    
    // Get API URL
    getApiUrl: (endpoint, params = {}) => {
        let url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_VERSION}${endpoint}`;
        
        // Replace path parameters
        Object.keys(params).forEach(key => {
            url = url.replace(`{${key}}`, params[key]);
        });
        
        return url;
    },
    
    // Get query string
    getQueryString: (params) => {
        const searchParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                searchParams.append(key, params[key]);
            }
        });
        return searchParams.toString();
    },
    
    // Truncate text
    truncateText: (text, maxLength = 100) => {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    },
    
    // Capitalize first letter
    capitalize: (str) => {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1);
    },
    
    // Get property type icon
    getPropertyTypeIcon: (type) => {
        const icons = {
            apartment: 'fas fa-building',
            house: 'fas fa-home',
            villa: 'fas fa-house-user',
            commercial: 'fas fa-store',
            plot: 'fas fa-map'
        };
        return icons[type] || 'fas fa-home';
    },
    
    // Get amenity icon
    getAmenityIcon: (amenity) => {
        const icons = {
            'Swimming Pool': 'fas fa-swimmer',
            'Gym': 'fas fa-dumbbell',
            'Garden': 'fas fa-seedling',
            'Security': 'fas fa-shield-alt',
            'Elevator': 'fas fa-elevator',
            'Power Backup': 'fas fa-bolt',
            'Water Supply': 'fas fa-tint',
            'Parking': 'fas fa-parking',
            'Playground': 'fas fa-child',
            'Club House': 'fas fa-users',
            'Shopping Center': 'fas fa-shopping-cart',
            'Hospital Nearby': 'fas fa-hospital',
            'School Nearby': 'fas fa-school',
            'Metro Station': 'fas fa-subway',
            'Bus Stop': 'fas fa-bus'
        };
        return icons[amenity] || 'fas fa-check';
    }
};

// Export for use in other files
window.API_CONFIG = API_CONFIG;
window.FIREBASE_CONFIG = FIREBASE_CONFIG;
window.APP_CONFIG = APP_CONFIG;
window.PROPERTY_OPTIONS = PROPERTY_OPTIONS;
window.USER_ROLES = USER_ROLES;
window.INVESTMENT_RISK_LEVELS = INVESTMENT_RISK_LEVELS;
window.SERVICE_CATEGORIES = SERVICE_CATEGORIES;
window.UTILS = UTILS;
