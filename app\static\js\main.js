// Main application file for DreamBig Real Estate Platform

class App {
    constructor() {
        this.currentSection = 'home';
        this.isInitialized = false;
        
        this.init();
    }
    
    // Initialize the application
    init() {
        this.setupEventListeners();
        this.setupNavigation();
        this.setupModals();
        this.setupToasts();
        this.checkInitialRoute();
        
        this.isInitialized = true;
        console.log('DreamBig Real Estate Platform initialized');
    }
    
    // Setup global event listeners
    setupEventListeners() {
        // Window events
        window.addEventListener('resize', this.handleResize.bind(this));
        window.addEventListener('scroll', this.handleScroll.bind(this));
        
        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // Click outside to close dropdowns
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    }
    
    // Setup navigation
    setupNavigation() {
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('href').substring(1);
                this.showSection(section);
            });
        });
        
        // Mobile menu toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }
    }
    
    // Setup modal functionality
    setupModals() {
        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    this.closeModal(openModal.id);
                }
            }
        });
    }
    
    // Setup toast notifications
    setupToasts() {
        // Create toast container if it doesn't exist
        if (!document.getElementById('toast-container')) {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
    }
    
    // Check initial route
    checkInitialRoute() {
        const hash = window.location.hash.substring(1);
        if (hash) {
            this.showSection(hash);
        } else {
            this.showSection('home');
        }
    }
    
    // Show section
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('main > section').forEach(section => {
            section.style.display = 'none';
        });
        
        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.style.display = 'block';
            this.currentSection = sectionName;
            
            // Update navigation
            this.updateNavigation(sectionName);
            
            // Update URL
            window.history.pushState(null, null, `#${sectionName}`);
            
            // Load section-specific data
            this.loadSectionData(sectionName);
        }
    }
    
    // Update navigation active state
    updateNavigation(activeSection) {
        document.querySelectorAll('.nav-link').forEach(link => {
            const href = link.getAttribute('href').substring(1);
            link.classList.toggle('active', href === activeSection);
        });
    }
    
    // Load section-specific data
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'properties':
                if (window.propertiesManager) {
                    propertiesManager.loadProperties(true);
                }
                break;
            case 'dashboard':
                if (window.dashboardManager && authManager.isAuthenticated()) {
                    dashboardManager.loadDashboardData();
                }
                break;
        }
    }
    
    // Handle window resize
    handleResize() {
        // Close mobile menu on resize
        const navMenu = document.querySelector('.nav-menu');
        const navToggle = document.querySelector('.nav-toggle');
        
        if (window.innerWidth > 768) {
            navMenu?.classList.remove('active');
            navToggle?.classList.remove('active');
        }
    }
    
    // Handle scroll events
    handleScroll() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.classList.toggle('scrolled', window.scrollY > 50);
        }
    }
    
    // Handle keyboard shortcuts
    handleKeydown(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                this.closeModal(openModal.id);
            }
        }
    }
    
    // Handle clicks outside elements
    handleOutsideClick(e) {
        // Close user dropdown
        const userMenu = document.getElementById('user-menu');
        const dropdown = document.getElementById('user-dropdown');
        
        if (userMenu && !userMenu.contains(e.target)) {
            dropdown?.classList.remove('show');
        }
        
        // Close mobile menu
        const navMenu = document.querySelector('.nav-menu');
        const navToggle = document.querySelector('.nav-toggle');
        
        if (navMenu && navToggle && 
            !navMenu.contains(e.target) && 
            !navToggle.contains(e.target) &&
            navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        }
    }
    
    // Close modal
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            
            // Reset form if exists
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
        }
    }
    
    // Show loading spinner
    showLoading(show = true) {
        const spinner = document.getElementById('loading-spinner');
        if (spinner) {
            spinner.style.display = show ? 'flex' : 'none';
        }
    }
    
    // Show toast notification
    showToast(message, type = 'info', duration = APP_CONFIG.TOAST_DURATION) {
        const container = document.getElementById('toast-container');
        if (!container) return;
        
        const toastId = UTILS.generateUUID();
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.id = toastId;
        
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        
        toast.innerHTML = `
            <div class="toast-header">
                <div class="toast-title">
                    <i class="fas ${icons[type] || icons.info}"></i>
                    ${UTILS.capitalize(type)}
                </div>
                <button class="toast-close" onclick="app.closeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-message">${message}</div>
        `;
        
        container.appendChild(toast);
        
        // Auto-remove toast
        setTimeout(() => {
            this.closeToast(toastId);
        }, duration);
    }
    
    // Close toast
    closeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.style.animation = 'toastSlideOut 0.3s ease forwards';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }
    
    // Mobile menu toggle
    toggleMobileMenu() {
        const navMenu = document.querySelector('.nav-menu');
        const navToggle = document.querySelector('.nav-toggle');
        
        navMenu?.classList.toggle('active');
        navToggle?.classList.toggle('active');
    }
}

// Initialize the application
const app = new App();

// Global utility functions
window.showSection = (sectionName) => {
    app.showSection(sectionName);
};

window.closeModal = (modalId) => {
    app.closeModal(modalId);
};

window.showLoading = (show = true) => {
    app.showLoading(show);
};

window.showToast = (message, type = 'info', duration = APP_CONFIG.TOAST_DURATION) => {
    app.showToast(message, type, duration);
};

window.toggleMobileMenu = () => {
    app.toggleMobileMenu();
};

// Add CSS animation for toast slide out
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideOut {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
    
    .navbar.scrolled {
        box-shadow: var(--shadow-md);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }
    
    .no-properties,
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: var(--text-secondary);
    }
    
    .dashboard-property-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: var(--transition);
    }
    
    .dashboard-property-item:hover {
        background: var(--background-color);
        border-color: var(--primary-color);
    }
    
    .property-thumbnail {
        width: 60px;
        height: 60px;
        border-radius: var(--border-radius);
        overflow: hidden;
        flex-shrink: 0;
    }
    
    .property-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .analytics-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .analytics-item {
        text-align: center;
        padding: 1rem;
        background: var(--background-color);
        border-radius: var(--border-radius);
    }
    
    .analytics-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .analytics-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }
    
    .kyc-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border-radius: var(--border-radius);
        font-weight: 500;
    }
    
    .kyc-status.verified {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
    }
    
    .kyc-status.pending {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
    }
`;
document.head.appendChild(style);

// Export app instance
window.app = app;
