{% extends "base.html" %}

{% block title %}Properties - DreamBig Real Estate Platform{% endblock %}

{% block content %}
    <!-- Properties Section -->
    <section class="properties-section" style="margin-top: 80px; padding-top: 2rem;">
        <div class="container">
            <div class="section-header">
                <h2>Properties</h2>
                <div class="view-controls">
                    <button class="view-btn active" data-view="grid"><i class="fas fa-th"></i></button>
                    <button class="view-btn" data-view="list"><i class="fas fa-list"></i></button>
                </div>
            </div>
            
            <!-- Search and Filters -->
            <div class="search-container" style="margin-bottom: 2rem;">
                <div class="search-bar">
                    <input type="text" id="search-input" placeholder="Search properties, locations, or keywords...">
                    <button class="search-btn" onclick="performSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="search-filters">
                    <select id="property-type">
                        <option value="">Property Type</option>
                        <option value="apartment">Apartment</option>
                        <option value="house">House</option>
                        <option value="villa">Villa</option>
                        <option value="commercial">Commercial</option>
                    </select>
                    
                    <select id="bhk-filter">
                        <option value="">BHK</option>
                        <option value="1">1 BHK</option>
                        <option value="2">2 BHK</option>
                        <option value="3">3 BHK</option>
                        <option value="4">4+ BHK</option>
                    </select>
                    
                    <input type="number" id="price-min" placeholder="Min Price">
                    <input type="number" id="price-max" placeholder="Max Price">
                    
                    <button class="btn btn-secondary" onclick="showAdvancedFilters()">
                        <i class="fas fa-filter"></i> More Filters
                    </button>
                </div>
            </div>
            
            <div class="properties-grid" id="properties-grid">
                <!-- Properties will be loaded here -->
            </div>
            
            <div class="load-more">
                <button class="btn btn-outline" onclick="loadMoreProperties()">Load More Properties</button>
            </div>
        </div>
    </section>
{% endblock %}

{% block extra_scripts %}
<script>
    // Initialize properties page
    document.addEventListener('DOMContentLoaded', function() {
        // Show properties section by default
        if (window.propertiesManager) {
            propertiesManager.loadProperties(true);
        }
    });
</script>
{% endblock %}
