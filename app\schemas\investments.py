from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
from typing import Optional
from app.db.models import InvestmentRiskLevel

class InvestmentBase(BaseModel):
    amount: float = Field(..., gt=0)
    expected_roi: float = Field(..., ge=0, le=100)
    risk_level: InvestmentRiskLevel
    start_date: datetime
    end_date: datetime

class InvestmentCreate(InvestmentBase):
    property_id: int = Field(..., gt=0)

class InvestmentOut(InvestmentBase):
    id: int
    investor_id: int
    property_id: int
    status: str
    created_at: datetime

    class Config:
        from_attributes = True

class InvestmentDocument(BaseModel):
    id: int
    name: str
    url: HttpUrl

    class Config:
        from_attributes = True