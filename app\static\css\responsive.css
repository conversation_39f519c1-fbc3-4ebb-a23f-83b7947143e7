/* Mobile First Responsive Design */

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .nav-container {
        padding: 1rem 1.5rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content p {
        font-size: 1.125rem;
    }
    
    .search-filters {
        gap: 0.75rem;
    }
    
    .properties-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Navigation Mobile */
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--surface-color);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-lg);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu .nav-link {
        padding: 1rem 0;
        font-size: 1.125rem;
        width: 100%;
        text-align: center;
        border-bottom: 1px solid var(--border-color);
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .auth-buttons {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        padding: 1rem 2rem;
        position: absolute;
        bottom: 2rem;
    }
    
    .nav-menu.active .auth-buttons {
        display: flex;
    }
    
    /* Hero Section Mobile */
    .hero-section {
        padding: 6rem 1rem 3rem;
    }
    
    .hero-content h1 {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }
    
    .hero-content p {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .search-container {
        max-width: 100%;
    }
    
    .search-bar {
        flex-direction: column;
    }
    
    .search-btn {
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }
    
    .search-filters {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .search-filters select,
    .search-filters input {
        width: 100%;
    }
    
    /* Properties Grid Mobile */
    .properties-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .section-header h2 {
        font-size: 1.5rem;
    }
    
    .property-card {
        margin: 0 10px;
    }
    
    .property-content {
        padding: 1rem;
    }
    
    .property-title {
        font-size: 1.125rem;
    }
    
    .property-details {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .property-detail {
        font-size: 0.8rem;
    }
    
    .property-price {
        font-size: 1.25rem;
    }
    
    .property-actions {
        flex-wrap: wrap;
    }
    
    /* Dashboard Mobile */
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .dashboard-card {
        padding: 1.5rem;
    }
    
    /* User Menu Mobile */
    .user-dropdown {
        position: fixed;
        top: 80px;
        right: 0;
        left: 0;
        border-radius: 0;
        box-shadow: var(--shadow-lg);
    }
    
    .user-avatar {
        padding: 0.5rem;
    }
    
    .user-avatar span {
        display: none;
    }
    
    /* Buttons Mobile */
    .btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }
    
    .btn-icon {
        padding: 0.75rem;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .nav-logo h2 {
        font-size: 1.25rem;
    }
    
    .hero-section {
        padding: 5rem 0.5rem 2rem;
    }
    
    .hero-content h1 {
        font-size: 1.75rem;
        line-height: 1.2;
    }
    
    .hero-content p {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
    
    .search-bar input {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }
    
    .search-btn {
        padding: 0.875rem 1rem;
    }
    
    .search-filters select,
    .search-filters input {
        padding: 0.625rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .property-image {
        height: 180px;
    }
    
    .property-content {
        padding: 0.875rem;
    }
    
    .property-title {
        font-size: 1rem;
    }
    
    .property-price {
        font-size: 1.125rem;
    }
    
    .dashboard-card {
        padding: 1rem;
    }
    
    .dashboard-card h3 {
        font-size: 1rem;
    }
    
    .modal-content {
        padding: 1rem;
        margin: 0.5rem;
    }
    
    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        padding: 4rem 1rem 2rem;
    }
    
    .hero-content h1 {
        font-size: 1.75rem;
    }
    
    .hero-content p {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
    
    .nav-menu {
        height: calc(100vh - 60px);
        top: 60px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .property-image img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .nav-toggle,
    .user-menu,
    .modal,
    .toast-container,
    .loading-spinner {
        display: none !important;
    }
    
    .hero-section {
        background: none !important;
        color: var(--text-primary) !important;
        padding: 2rem 0 !important;
    }
    
    .property-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid var(--border-color) !important;
    }
    
    .btn {
        display: none !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .modal-content {
        animation: none;
    }
    
    .toast {
        animation: none;
    }
    
    .spinner {
        animation: none;
        border-top-color: transparent;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #0f172a;
        --surface-color: #1e293b;
        --text-primary: #f1f5f9;
        --text-secondary: #94a3b8;
        --border-color: #334155;
    }
    
    .search-filters select,
    .search-filters input {
        background: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.2);
    }
}

/* Focus Styles for Accessibility */
@media (prefers-reduced-motion: no-preference) {
    *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    .btn:focus,
    .nav-link:focus {
        outline-offset: 4px;
    }
}
