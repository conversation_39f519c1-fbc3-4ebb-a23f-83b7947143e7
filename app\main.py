from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.api.v1.routers import api_router
from app.core.firebase import initialize_firebase # type: ignore
from app.db.session import engine
from app.db import models
from app.config import settings

# Create database tables
models.Base.metadata.create_all(bind=engine)

# Initialize Firebase
initialize_firebase()

app = FastAPI(
    title="DreamBig Real Estate Platform",
    description="Complete property listing and investment platform",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Include API routers
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
def root():
    return FileResponse("app/static/index.html")

@app.get("/favicon.ico")
def favicon():
    return FileResponse("app/static/images/favicon.ico")