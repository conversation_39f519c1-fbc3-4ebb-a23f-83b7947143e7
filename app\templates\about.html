{% extends "base.html" %}

{% block title %}About Us - DreamBig Real Estate Platform{% endblock %}

{% block content %}
    <!-- About Section -->
    <section class="about-section" style="margin-top: 80px; padding: 4rem 0;">
        <div class="container">
            <!-- Hero Section -->
            <div class="about-hero" style="text-align: center; margin-bottom: 4rem;">
                <h1 style="font-size: 3rem; font-weight: 700; color: var(--primary-color); margin-bottom: 1rem;">About DreamBig</h1>
                <p style="font-size: 1.25rem; color: var(--text-secondary); max-width: 600px; margin: 0 auto;">
                    Revolutionizing real estate with AI-powered solutions for property discovery, investment opportunities, and professional services.
                </p>
            </div>
            
            <!-- Mission & Vision -->
            <div class="mission-vision" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 3rem; margin-bottom: 4rem;">
                <div class="mission-card" style="background: var(--surface-color); padding: 2rem; border-radius: var(--border-radius); text-align: center; box-shadow: var(--shadow-sm);">
                    <div class="icon" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3 style="margin-bottom: 1rem; color: var(--primary-color);">Our Mission</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">
                        To make real estate accessible, transparent, and efficient for everyone through innovative technology and personalized experiences.
                    </p>
                </div>
                
                <div class="vision-card" style="background: var(--surface-color); padding: 2rem; border-radius: var(--border-radius); text-align: center; box-shadow: var(--shadow-sm);">
                    <div class="icon" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 style="margin-bottom: 1rem; color: var(--primary-color);">Our Vision</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">
                        To become the leading platform that connects property seekers, investors, and service providers in a seamless ecosystem.
                    </p>
                </div>
            </div>
            
            <!-- Features -->
            <div class="features-section" style="margin-bottom: 4rem;">
                <h2 style="text-align: center; margin-bottom: 3rem; color: var(--primary-color);">Why Choose DreamBig?</h2>
                
                <div class="features-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                    <div class="feature-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); box-shadow: var(--shadow-sm);">
                        <div class="feature-icon" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem;">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">AI-Powered Search</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            Advanced AI algorithms help you find the perfect property based on your preferences and behavior.
                        </p>
                    </div>
                    
                    <div class="feature-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); box-shadow: var(--shadow-sm);">
                        <div class="feature-icon" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem;">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Verified Properties</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            All properties undergo thorough verification to ensure authenticity and prevent fraud.
                        </p>
                    </div>
                    
                    <div class="feature-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); box-shadow: var(--shadow-sm);">
                        <div class="feature-icon" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem;">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Investment Insights</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            Get detailed analytics and insights to make informed investment decisions.
                        </p>
                    </div>
                    
                    <div class="feature-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); box-shadow: var(--shadow-sm);">
                        <div class="feature-icon" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem;">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Professional Services</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            Access to verified legal, financial, and maintenance services for all your property needs.
                        </p>
                    </div>
                    
                    <div class="feature-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); box-shadow: var(--shadow-sm);">
                        <div class="feature-icon" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem;">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Mobile Optimized</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            Fully responsive design that works seamlessly across all devices and platforms.
                        </p>
                    </div>
                    
                    <div class="feature-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); box-shadow: var(--shadow-sm);">
                        <div class="feature-icon" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem;">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">24/7 Support</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            Round-the-clock customer support to assist you with any queries or issues.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="stats-section" style="background: var(--primary-color); color: white; padding: 3rem 2rem; border-radius: var(--border-radius); margin-bottom: 4rem;">
                <h2 style="text-align: center; margin-bottom: 2rem;">Our Impact</h2>
                
                <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; text-align: center;">
                    <div class="stat-item">
                        <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">10,000+</div>
                        <div class="stat-label" style="opacity: 0.9;">Properties Listed</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">5,000+</div>
                        <div class="stat-label" style="opacity: 0.9;">Happy Customers</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">50+</div>
                        <div class="stat-label" style="opacity: 0.9;">Cities Covered</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">₹100Cr+</div>
                        <div class="stat-label" style="opacity: 0.9;">Transactions Facilitated</div>
                    </div>
                </div>
            </div>
            
            <!-- Team Section -->
            <div class="team-section" style="margin-bottom: 4rem;">
                <h2 style="text-align: center; margin-bottom: 3rem; color: var(--primary-color);">Our Team</h2>
                
                <div class="team-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                    <div class="team-member" style="background: var(--surface-color); padding: 2rem; border-radius: var(--border-radius); text-align: center; box-shadow: var(--shadow-sm);">
                        <div class="member-avatar" style="width: 100px; height: 100px; background: var(--primary-color); border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                            <i class="fas fa-user"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">John Doe</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;">CEO & Founder</p>
                        <p style="color: var(--text-secondary); font-size: 0.85rem;">
                            Visionary leader with 15+ years in real estate and technology.
                        </p>
                    </div>
                    
                    <div class="team-member" style="background: var(--surface-color); padding: 2rem; border-radius: var(--border-radius); text-align: center; box-shadow: var(--shadow-sm);">
                        <div class="member-avatar" style="width: 100px; height: 100px; background: var(--primary-color); border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                            <i class="fas fa-user"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Jane Smith</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;">CTO</p>
                        <p style="color: var(--text-secondary); font-size: 0.85rem;">
                            Tech expert specializing in AI and machine learning applications.
                        </p>
                    </div>
                    
                    <div class="team-member" style="background: var(--surface-color); padding: 2rem; border-radius: var(--border-radius); text-align: center; box-shadow: var(--shadow-sm);">
                        <div class="member-avatar" style="width: 100px; height: 100px; background: var(--primary-color); border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                            <i class="fas fa-user"></i>
                        </div>
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Mike Johnson</h4>
                        <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;">Head of Operations</p>
                        <p style="color: var(--text-secondary); font-size: 0.85rem;">
                            Operations specialist ensuring smooth platform functionality.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Contact CTA -->
            <div class="contact-cta" style="background: var(--background-color); padding: 3rem 2rem; border-radius: var(--border-radius); text-align: center;">
                <h2 style="margin-bottom: 1rem; color: var(--primary-color);">Ready to Get Started?</h2>
                <p style="color: var(--text-secondary); margin-bottom: 2rem; max-width: 500px; margin-left: auto; margin-right: auto;">
                    Join thousands of users who have found their dream properties and investment opportunities with DreamBig.
                </p>
                
                <div class="cta-buttons" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="showRegisterModal()">
                        <i class="fas fa-user-plus"></i> Sign Up Now
                    </button>
                    <button class="btn btn-outline" onclick="window.location.href='/properties'">
                        <i class="fas fa-search"></i> Browse Properties
                    </button>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
