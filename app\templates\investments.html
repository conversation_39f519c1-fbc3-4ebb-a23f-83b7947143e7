{% extends "base.html" %}

{% block title %}Investments - DreamBig Real Estate Platform{% endblock %}

{% block content %}
    <!-- Investments Section -->
    <section class="investments-section" style="margin-top: 80px; padding-top: 2rem;">
        <div class="container">
            <div class="section-header">
                <h2>Investment Opportunities</h2>
                <div class="investment-actions">
                    <button class="btn btn-primary" onclick="showCreateInvestmentModal()">
                        <i class="fas fa-plus"></i> New Investment
                    </button>
                </div>
            </div>
            
            <!-- Investment Stats -->
            <div class="investment-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--primary-color);">₹0</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Total Invested</div>
                </div>
                
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--success-color);">₹0</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Current Value</div>
                </div>
                
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--accent-color);">0%</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Average ROI</div>
                </div>
                
                <div class="stat-card" style="background: var(--surface-color); padding: 1.5rem; border-radius: var(--border-radius); text-align: center;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: 700; color: var(--primary-color);">0</div>
                    <div class="stat-label" style="color: var(--text-secondary);">Active Investments</div>
                </div>
            </div>
            
            <!-- Investment Filters -->
            <div class="investment-filters" style="margin-bottom: 2rem;">
                <div class="filter-group" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <select id="risk-filter" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                        <option value="">All Risk Levels</option>
                        <option value="low">Low Risk</option>
                        <option value="medium">Medium Risk</option>
                        <option value="high">High Risk</option>
                    </select>
                    
                    <select id="status-filter" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                    </select>
                    
                    <input type="number" id="min-amount" placeholder="Min Amount" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                    <input type="number" id="max-amount" placeholder="Max Amount" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                </div>
            </div>
            
            <!-- Investments Grid -->
            <div class="investments-grid" id="investments-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 2rem;">
                <!-- Investments will be loaded here -->
            </div>
            
            <!-- Empty State -->
            <div id="investments-empty" class="empty-state" style="text-align: center; padding: 3rem 1rem; color: var(--text-secondary);">
                <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <h3>No Investments Yet</h3>
                <p>Start your investment journey by exploring available opportunities</p>
                <div style="margin-top: 2rem;">
                    <button class="btn btn-primary" onclick="showCreateInvestmentModal()">Create Investment</button>
                    <button class="btn btn-outline" onclick="window.location.href='/properties'">Browse Properties</button>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Authentication Required Message -->
    <div id="auth-required" class="auth-required" style="display: none; text-align: center; padding: 4rem 2rem; margin-top: 80px;">
        <div class="container">
            <i class="fas fa-lock" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
            <h2>Authentication Required</h2>
            <p>Please login to view your investments</p>
            <div style="margin-top: 2rem;">
                <button class="btn btn-primary" onclick="showLoginModal()">Login</button>
                <button class="btn btn-outline" onclick="showRegisterModal()">Sign Up</button>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Initialize investments page
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is authenticated
        setTimeout(function() {
            if (authManager && authManager.isAuthenticated()) {
                document.querySelector('.investments-section').style.display = 'block';
                document.getElementById('auth-required').style.display = 'none';
                loadInvestments();
            } else {
                document.querySelector('.investments-section').style.display = 'none';
                document.getElementById('auth-required').style.display = 'block';
            }
        }, 500);
    });
    
    // Listen for auth state changes
    document.addEventListener('authStateChanged', function(e) {
        if (e.detail.isAuthenticated) {
            document.querySelector('.investments-section').style.display = 'block';
            document.getElementById('auth-required').style.display = 'none';
            loadInvestments();
        } else {
            document.querySelector('.investments-section').style.display = 'none';
            document.getElementById('auth-required').style.display = 'block';
        }
    });
    
    // Load investments
    async function loadInvestments() {
        if (!apiClient) return;
        
        const response = await ApiUtils.handleApiCall(
            () => apiClient.getInvestments(),
            { showLoading: true }
        );
        
        if (response) {
            renderInvestments(response);
        }
    }
    
    // Render investments
    function renderInvestments(investments) {
        const grid = document.getElementById('investments-grid');
        const emptyState = document.getElementById('investments-empty');
        
        if (investments.length === 0) {
            grid.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }
        
        grid.style.display = 'grid';
        emptyState.style.display = 'none';
        
        grid.innerHTML = investments.map(investment => createInvestmentCard(investment)).join('');
    }
    
    // Create investment card
    function createInvestmentCard(investment) {
        const riskColor = {
            low: 'var(--success-color)',
            medium: 'var(--warning-color)',
            high: 'var(--error-color)'
        };
        
        return `
            <div class="investment-card" style="background: var(--surface-color); border-radius: var(--border-radius); padding: 1.5rem; box-shadow: var(--shadow-sm);">
                <div class="investment-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h3 style="color: var(--primary-color);">Investment #${investment.id}</h3>
                    <span class="risk-badge" style="background: ${riskColor[investment.risk_level]}; color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.75rem;">
                        ${investment.risk_level.toUpperCase()} RISK
                    </span>
                </div>
                
                <div class="investment-details" style="margin-bottom: 1rem;">
                    <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Amount:</span>
                        <strong>${UTILS.formatCurrency(investment.amount)}</strong>
                    </div>
                    <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Expected ROI:</span>
                        <strong>${investment.expected_roi}%</strong>
                    </div>
                    <div class="detail-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Status:</span>
                        <span class="status-badge" style="color: var(--primary-color); font-weight: 500;">${investment.status}</span>
                    </div>
                    <div class="detail-row" style="display: flex; justify-content: space-between;">
                        <span>Start Date:</span>
                        <span>${UTILS.formatDate(investment.start_date)}</span>
                    </div>
                </div>
                
                <div class="investment-actions" style="display: flex; gap: 0.5rem;">
                    <button class="btn btn-outline" onclick="viewInvestmentDetails(${investment.id})" style="flex: 1;">
                        View Details
                    </button>
                    <button class="btn-icon" onclick="shareInvestment(${investment.id})" title="Share">
                        <i class="fas fa-share-alt"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    // Investment actions
    function showCreateInvestmentModal() {
        showToast('Investment creation feature coming soon!', 'info');
    }
    
    function viewInvestmentDetails(investmentId) {
        showToast('Investment details feature coming soon!', 'info');
    }
    
    function shareInvestment(investmentId) {
        showToast('Investment sharing feature coming soon!', 'info');
    }
</script>
{% endblock %}
